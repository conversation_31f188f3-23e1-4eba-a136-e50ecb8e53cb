import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { toast } from "sonner"; // Import sonner toast for success/error notifications
import {
  ExpenseCategory,
  fetchCategories
} from "@/integrations/supabase/schema";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Plus } from "lucide-react";
import { ExpenseLineItem as ExpenseLineItemComponent } from "./ExpenseLineItem";
import { 
  getExpenseById, 
  updateExpenseWithLineItems, 
  createExpense 
} from "@/integrations/supabase/api";
import { supabase } from "@/integrations/supabase/client"; // Import the supabase client

// Define schema for line item with most fields required
const lineItemSchema = z.object({
  id: z.string().optional(),
  // Now description is required (not optional)
  description: z.string().min(1, { message: "Description is required." }),
  amount: z.coerce.number().positive({ message: "Amount must be positive." }),
  currency: z.string().min(1, { message: "Currency is required." }).default("USD"),
  date: z.string().min(1, { message: "Date is required." }),
  // Category is now required
  category_id: z.string().min(1, { message: "Category is required." }),
  receipt_url: z.string().optional(), // Receipt remains optional
  receipt_file: z.any().optional(), // To handle temporary file uploads
});

// Define schema for form validation with multiple line items
const expenseFormSchema = z.object({
  title: z.string().min(2, { message: "Title must be at least 2 characters." }),
  // Description is now required
  description: z.string().min(1, { message: "Description is required." }),
  lineItems: z.array(lineItemSchema).min(1, { message: "At least one line item is required." }),
});

type ExpenseFormValues = z.infer<typeof expenseFormSchema>;

interface ExpenseFormProps {
  initialData?: Partial<ExpenseFormValues>;
}

export function ExpenseForm({ initialData }: ExpenseFormProps) {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast: hookToast } = useToast(); // Rename to avoid conflict with sonner toast
  const [categories, setCategories] = useState<ExpenseCategory[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize form with default values or initial data
  const form = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseFormSchema) as any,
    defaultValues: {
      title: initialData?.title || "",
      description: initialData?.description || "",
      lineItems: initialData?.lineItems || [{
        amount: 0,
        currency: "USD",
        date: new Date().toISOString().substring(0, 10),
        category_id: "",
        description: "",
        receipt_url: "",
      }]
    },
  });

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      console.log("Updating form with new initialData:", initialData);
      form.reset({
        title: initialData.title || "",
        description: initialData.description || "",
        lineItems: initialData.lineItems?.length ? initialData.lineItems.map(item => ({
          ...item,
          currency: item.currency || "USD"
        })) : [{
          amount: 0,
          currency: "USD",
          date: new Date().toISOString().substring(0, 10),
          category_id: "",
          description: "",
          receipt_url: "",
        }]
      });
    }
  }, [initialData, form]);

  // Load expense categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        // Use our helper function for better type safety
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);
      } catch (error: any) {
        hookToast({
          title: "Error loading categories",
          description: error.message,
          variant: "destructive",
        });
      }
    };

    loadCategories();
  }, [hookToast]);

  // Load expense details if editing an existing expense
  useEffect(() => {
    if (!id) return;

    const loadExpense = async () => {
      setIsLoading(true);
      
      try {
        // Use the API function to get expense with line items
        const expenseData = await getExpenseById(id);

        if (expenseData) {
          // If we have line items, use them
          if (expenseData.lineItems && expenseData.lineItems.length > 0) {
            form.reset({
              title: expenseData.title,
              description: expenseData.description || "",
              lineItems: expenseData.lineItems.map((item: any) => ({
                id: item.id,
                amount: item.amount,
                currency: item.currency || "USD",
                date: new Date(item.date).toISOString().substring(0, 10),
                category_id: item.category_id || "",
                description: item.description || "",
                receipt_url: item.receipt_url || "",
              }))
            });
          } else {
            // For backward compatibility with older expenses without line items
            form.reset({
              title: expenseData.title,
              description: expenseData.description || "",
              lineItems: [{
                amount: expenseData.amount,
                currency: expenseData.currency || "USD",
                date: new Date(expenseData.date).toISOString().substring(0, 10),
                category_id: expenseData.category_id || "",
                description: expenseData.description || "",
                receipt_url: expenseData.receipt_url || "",
              }]
            });
          }
        } else {
          // Handle case when no expense is found
          hookToast({
            title: "Expense not found",
            description: "The requested expense could not be found.",
            variant: "destructive",
          });
          navigate("/expenses");
        }
      } catch (error: any) {
        hookToast({
          title: "Error loading expense",
          description: error.message,
          variant: "destructive",
        });
        navigate("/expenses");
      } finally {
        setIsLoading(false);
      }
    };

    loadExpense();
  }, [id, form, navigate, hookToast]);

  // Add a new line item
  const addLineItem = () => {
    const currentLineItems = form.getValues("lineItems") || [];
    
    form.setValue("lineItems", [
      ...currentLineItems,
      {
        amount: 0,
        currency: "USD",
        date: new Date().toISOString().substring(0, 10),
        category_id: "",
        description: "",
        receipt_url: "",
      }
    ]);
  };

  // Remove a line item
  const removeLineItem = (index: number) => {
    const currentLineItems = form.getValues("lineItems") || [];
    
    // Ensure we always have at least one line item
    if (currentLineItems.length <= 1) {
      toast.error("At least one line item is required.");
      return;
    }
    
    form.setValue("lineItems", 
      currentLineItems.filter((_, idx) => idx !== index)
    );
  };

  // Calculate total expense amount
  const calculateTotal = () => {
    const lineItems = form.getValues("lineItems") || [];
    return lineItems.reduce((sum, item) => sum + (Number(item.amount) || 0), 0);
  };

  // Handle form submission
  const onSubmit = async (values: ExpenseFormValues) => {
    console.log("Form submission started with values:", JSON.stringify(values, null, 2));
    setIsSubmitting(true);
    
    try {
      // Process line items to handle file uploads
      console.log("Processing line items for submission...");
      const processedLineItems = await Promise.all(values.lineItems.map(async (item, index) => {
        console.log(`Processing line item #${index}:`, item);
        
        // If there's a receipt file to upload
        if (item.receipt_file && typeof item.receipt_file !== 'string') {
          console.log(`Line item #${index} has a file to upload`, item.receipt_file);
          try {
            // Generate a temporary ID for the file if we don't have an item ID yet
            const tempId = item.id || `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
            
            // Upload the file to Supabase Storage
            const fileExt = item.receipt_file.name.split('.').pop();
            const fileName = `temp-uploads/${tempId}/${Date.now()}.${fileExt}`;
            
            console.log(`Uploading file for line item #${index} to ${fileName}`);
            
            const { error: uploadError } = await supabase.storage
              .from('expense-imgs')
              .upload(fileName, item.receipt_file, {
                upsert: true,
              });
              
            if (uploadError) {
              console.error("File upload error:", uploadError);
              throw uploadError;
            }
            
            // Get the public URL
            const { data: urlData } = supabase.storage
              .from('expense-imgs')
              .getPublicUrl(fileName);
              
            console.log(`File upload successful. Public URL: ${urlData.publicUrl}`);
            
            // Return the item with the updated receipt URL and ensure amount is a number
            return {
              ...item,
              // Ensure amount is a number (required by LineItemData)
              amount: Number(item.amount),
              receipt_url: urlData.publicUrl,
            };
          } catch (error) {
            console.error("Error uploading receipt:", error);
            // Still ensure amount is a number even if upload fails
            return {
              ...item,
              amount: Number(item.amount)
            };
          }
        } else if (item.receipt_url) {
          // If we already have a receipt URL (from AI fill for example), keep it
          console.log(`Line item #${index} already has a receipt URL: ${item.receipt_url}`);
          return {
            ...item,
            amount: Number(item.amount)
          };
        } else {
          console.log(`Line item #${index} has no receipt`);
        }
        
        // Return item with guaranteed number for amount
        return {
          ...item,
          amount: Number(item.amount)
        };
      }));
      
      console.log("Processed line items:", JSON.stringify(processedLineItems, null, 2));
      
      if (id) {
        // Update existing expense
        console.log(`Updating existing expense with ID: ${id}`);
        const success = await updateExpenseWithLineItems(
          id, 
          {
            title: values.title,
            description: values.description || ""
          },
          processedLineItems
        );

        if (!success) {
          console.error("Failed to update expense - API returned false");
          throw new Error("Failed to update expense");
        }

        console.log("Expense updated successfully");
        toast.success("Expense updated successfully");
      } else {
        // Create new expense
        console.log("Creating new expense");
        const newExpenseId = await createExpense(
          {
            title: values.title,
            description: values.description || ""
          },
          processedLineItems
        );

        if (!newExpenseId) {
          console.error("Failed to create expense - API returned null ID");
          throw new Error("Failed to create expense");
        }

        console.log(`New expense created with ID: ${newExpenseId}`);
        toast.success("Expense created successfully");
      }

      // Redirect to expenses list
      console.log("Redirecting to expenses list");
      navigate("/expenses");
    } catch (error: any) {
      console.error("Form submission error:", error);
      toast.error(error.message || "An unexpected error occurred");
    } finally {
      console.log("Form submission completed");
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <p>Loading expense details...</p>
      </div>
    );
  }

  // Main form rendering
  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{id ? "Edit Expense" : "Create New Expense"}</CardTitle>
        <CardDescription>
          {id 
            ? "Update your expense information" 
            : "Enter the details of your new expense"}
        </CardDescription>
      </CardHeader>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit as any)}>
          <CardContent className="space-y-6">
            {/* General expense information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">General Information</h3>
              
              <FormField
                control={form.control as any}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expense Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Expense title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control as any}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Provide details about this expense" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Line Items section */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Line Items</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addLineItem}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Line Item
                </Button>
              </div>
              
              <div className="space-y-4">
                {form.watch("lineItems").map((_, index) => (
                  <ExpenseLineItemComponent
                    key={index}
                    index={index}
                    categories={categories}
                    onRemove={() => removeLineItem(index)}
                    isRemoveDisabled={form.watch("lineItems").length <= 1}
                    lineItemId={form.getValues("lineItems")[index]?.id}
                  />
                ))}
              </div>
              
              <div className="text-right">
                <h4 className="text-lg font-bold">
                  Total: {calculateTotal().toFixed(2)} {form.watch("lineItems")[0]?.currency || "USD"}
                </h4>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => navigate("/expenses")}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className={isSubmitting ? "opacity-70 cursor-not-allowed" : ""}
            >
              {isSubmitting
                ? "Saving..."
                : id
                  ? "Save Changes"
                  : "Create Expense"
              }
            </Button>
          </CardFooter>
        </form>
      </FormProvider>
    </Card>
  );
}
