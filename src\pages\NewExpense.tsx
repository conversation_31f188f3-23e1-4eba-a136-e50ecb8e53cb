import { useState } from "react";
import { ExpenseForm } from "@/components/expense/ExpenseForm";
import { ExpenseCategory } from "@/integrations/supabase/schema";

// No changes needed to this file as it uses ExpenseForm which already uses the correct function

const NewExpense = () => {
  // Form data state with proper typing for multiple line items
  const [,] = useState({
    title: "",
    description: "",
    // Multiple line items
    lineItems: [
      {
        amount: 0,
        date: new Date().toISOString().slice(0, 10),
        description: "",
        category_id: "",
        currency: "USD",
        receipt_url: ""
      }
    ]
  });

  const [,] = useState<ExpenseCategory[]>([]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Create New Expense</h1>
      
      <div className="bg-white rounded-lg shadow-sm">
        <ExpenseForm />
      </div>
    </div>
  );
};

export default NewExpense;
