
import { useState, useEffect } from "react";
import {
  Card,
  CardContent
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>alog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";
import { genericSupabase } from "@/integrations/supabase/client";
import { Tag } from "@/types";

const TagsPage = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newTagName, setNewTagName] = useState("");
  const [editingTag, setEditingTag] = useState<Tag | null>(null);

  useEffect(() => {
    fetchTags();
  }, []);

  const fetchTags = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await genericSupabase
        .from('tags')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;
      
      setTags(data || []);
    } catch (error) {
      console.error("Error fetching tags:", error);
      toast.error("Failed to load tags");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDialog = (tag?: Tag) => {
    if (tag) {
      setEditingTag(tag);
      setNewTagName(tag.name);
    } else {
      setEditingTag(null);
      setNewTagName("");
    }
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!newTagName.trim()) {
      toast.error("Tag name cannot be empty");
      return;
    }

    setIsSubmitting(true);
    try {
      if (editingTag) {
        // Update existing tag
        const { error } = await genericSupabase
          .from('tags')
          .update({ name: newTagName })
          .eq('id', editingTag.id);

        if (error) throw error;
        
        toast.success("Tag updated successfully");
      } else {
        // Create new tag
        const { error } = await genericSupabase
          .from('tags')
          .insert({ name: newTagName });

        if (error) throw error;
        
        toast.success("Tag created successfully");
      }
      
      setIsDialogOpen(false);
      fetchTags();
    } catch (error) {
      console.error("Error saving tag:", error);
      toast.error("Failed to save tag");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (tagId: string) => {
    if (!confirm("Are you sure you want to delete this tag? This action cannot be undone.")) {
      return;
    }

    try {
      // First remove tag from ticket_tags junction table
      const { error: junctionError } = await genericSupabase
        .from('ticket_tags')
        .delete()
        .eq('tag_id', tagId);

      if (junctionError) throw junctionError;
      
      // Then delete the tag
      const { error } = await genericSupabase
        .from('tags')
        .delete()
        .eq('id', tagId);

      if (error) throw error;
      
      toast.success("Tag deleted successfully");
      fetchTags();
    } catch (error) {
      console.error("Error deleting tag:", error);
      toast.error("Failed to delete tag");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Tags</h1>
          <p className="text-muted-foreground">Manage tags for ticket categorization.</p>
        </div>
        <Button onClick={() => handleOpenDialog()}>Add Tag</Button>
      </div>

      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading tags...</p>
            </div>
          ) : tags.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40 text-center p-4">
              <p className="text-muted-foreground">No tags found</p>
              <Button 
                variant="link" 
                onClick={() => handleOpenDialog()}
                className="mt-2"
              >
                Add your first tag
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tag Name</TableHead>
                  <TableHead className="w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tags.map((tag) => (
                  <TableRow key={tag.id}>
                    <TableCell>{tag.name}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleOpenDialog(tag)}
                        >
                          Edit
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDelete(tag.id)}
                          className="text-destructive"
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Tag Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingTag ? "Edit Tag" : "Add Tag"}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="tag-name" className="text-sm font-medium">Tag Name</label>
              <Input
                id="tag-name"
                value={newTagName}
                onChange={(e) => setNewTagName(e.target.value)}
                placeholder="Enter tag name"
              />
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDialogOpen(false)} 
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TagsPage;
