import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

type TeamMember = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  department: string | null;
  role: string;
  manager_id: string | null;
};

type ManagerInfo = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  department: string | null;
  role: string;
};

export default function Team() {
  // State for the current user, their manager, their team members, and their direct reports
  const [currentUser, setCurrentUser] = useState<TeamMember | null>(null);
  const [managerInfo, setManagerInfo] = useState<ManagerInfo | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [directReports, setDirectReports] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamData = async () => {
      try {
        // Get current authenticated user
        const { data: authUser } = await supabase.auth.getUser();
        if (!authUser?.user) {
          setLoading(false);
          return;
        }

        // Get the current user's profile
        const { data: userProfile } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", authUser.user.id)
          .single();
        
        if (userProfile) {
          setCurrentUser(userProfile);
          
          // Fetch the user's manager if they have one
          if (userProfile.manager_id) {
            const { data: manager } = await supabase
              .from("profiles")
              .select("*")
              .eq("id", userProfile.manager_id)
              .single();
              
            if (manager) {
              setManagerInfo(manager);
            }
          }
          
          // Fetch team members (people with the same manager)
          if (userProfile.manager_id) {
            const { data: teammates } = await supabase
              .from("profiles")
              .select("*")
              .eq("manager_id", userProfile.manager_id)
              .neq("id", authUser.user.id); // Exclude the current user
            
            if (teammates) {
              setTeamMembers(teammates);
            }
          } 
          // If no manager, get people in the same department
          else if (userProfile.department) {
            const { data: departmentMembers } = await supabase
              .from("profiles")
              .select("*")
              .eq("department", userProfile.department)
              .neq("id", authUser.user.id); // Exclude the current user
            
            if (departmentMembers) {
              setTeamMembers(departmentMembers);
            }
          }
          
          // Get direct reports (people who have this user as their manager)
          const { data: reports } = await supabase
            .from("profiles")
            .select("*")
            .eq("manager_id", authUser.user.id);
            
          if (reports && reports.length > 0) {
            setDirectReports(reports);
          }
        }
      } catch (error) {
        console.error("Error fetching team data:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTeamData();
  }, []);

  const getInitials = (firstName: string, lastName: string) => {
    return (firstName?.charAt(0) || "") + (lastName?.charAt(0) || "");
  };

  if (loading) {
    return (
      <div className="flex justify-center my-8">
        <p>Loading team information...</p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* My Info Section */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight mb-4">My Info</h1>
        {currentUser ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarFallback className="text-lg">
                    {getInitials(currentUser.first_name, currentUser.last_name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-xl">
                    {currentUser.first_name} {currentUser.last_name}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">{currentUser.role}</p>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="text-sm"><span className="font-semibold">Email:</span> {currentUser.email}</p>
                {currentUser.department && (
                  <p className="text-sm"><span className="font-semibold">Department:</span> {currentUser.department}</p>
                )}
                {managerInfo && (
                  <div className="pt-2">
                    <p className="text-sm font-semibold">My Manager:</p>
                    <div className="flex items-center gap-2 mt-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {getInitials(managerInfo.first_name, managerInfo.last_name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">{managerInfo.first_name} {managerInfo.last_name}</p>
                        <p className="text-xs text-muted-foreground">{managerInfo.role}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        ) : (
          <p>User information not available.</p>
        )}
      </div>

      <Separator />

      {/* Direct Reports Section */}
      {directReports.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold tracking-tight mb-4">My Direct Reports</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {directReports.map((member) => (
              <Card key={member.id}>
                <CardHeader className="flex flex-row items-center gap-4 pb-2">
                  <Avatar>
                    <AvatarFallback>{getInitials(member.first_name, member.last_name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">
                      {member.first_name} {member.last_name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">{member.role}</p>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{member.email}</p>
                  {/* Always display department, even if null */}
                  <p className="text-sm text-muted-foreground mt-1">
                    Department: {member.department || "Not assigned"}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Team Members Section */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-4">My Team</h2>

        {loading ? (
          <div className="flex justify-center my-8">
            <p>Loading team members...</p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {teamMembers.map((member) => (
              <Card key={member.id}>
                <CardHeader className="flex flex-row items-center gap-4 pb-2">
                  <Avatar>
                    <AvatarFallback>{getInitials(member.first_name, member.last_name)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">
                      {member.first_name} {member.last_name}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">{member.role}</p>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{member.email}</p>
                  {/* Always display department, even if null */}
                  <p className="text-sm text-muted-foreground mt-1">
                    Department: {member.department || "Not assigned"}
                  </p>
                </CardContent>
              </Card>
            ))}

            {teamMembers.length === 0 && (
              <div className="col-span-full text-center py-8">
                <p className="text-muted-foreground">No team members found.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
