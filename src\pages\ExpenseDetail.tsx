// Add or modify necessary imports to use the ExpenseLineItem component
import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { Separator } from "@/components/ui/separator";
import { getExpenseById, updateExpenseStatus, getExpenseHistory, getAllExpenseCategories } from "@/integrations/supabase/api";
import { Loader2, Clock, Check, X, ArrowLeft, Edit, Send } from "lucide-react";
import { toast } from "sonner";
import { FormProvider, useForm } from "react-hook-form";
import { ExpenseLineItem } from "@/components/expense/ExpenseLineItem";
import { Dialog, DialogContent, DialogHeader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { supabase } from "@/integrations/supabase/client";

// Define the ExpenseHistory interface
interface ExpenseHistory {
  id: string;
  expense_id: string;
  action: string;
  old_status: string | null;
  new_status: string | null;
  user_id: string;
  notes?: string | null; // Add notes field for rejection comments
  created_at: string;
  profiles?: {
    first_name?: string;
    last_name?: string;
  };
}

export default function ExpenseDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [expense, setExpense] = useState<any>(null);
  const [expenseHistory, setExpenseHistory] = useState<ExpenseHistory[]>([]);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [Comment, setComment] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  
  // Set up form context for line items
  const form = useForm({
    defaultValues: {
      lineItems: []
    }
  });
  
  useEffect(() => {
    const loadExpenseAndCategories = async () => {
      setLoading(true);
      try {
        if (!id) {
          toast.error("Invalid expense ID");
          navigate("/expenses");
          return;
        }

        // Load expense data
        const data = await getExpenseById(id);
        if (!data) {
          toast.error("Expense not found");
          navigate("/expenses");
          return;
        }

        setExpense(data);

        // Load expense history
        const history = await getExpenseHistory(id);
        setExpenseHistory(history);
        
        // Load categories for displaying category names
        const categoriesData = await getAllExpenseCategories();
        setCategories(categoriesData);
        
        // Set up form data for line items display
        if (data.lineItems && data.lineItems.length > 0) {
          const formattedLineItems = data.lineItems.map((item: any) => ({
            id: item.id,
            amount: item.amount,
            currency: item.currency,
            date: item.date ? new Date(item.date).toISOString().substring(0, 10) : "",
            category_id: item.category_id || "",
            description: item.description || "",
            receipt_url: item.receipt_url || "",
          }));
          
          form.reset({ lineItems: formattedLineItems });
        }
      } catch (error) {
        console.error("Error loading expense:", error);
        toast.error("Failed to load expense details");
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      loadExpenseAndCategories();
    }
  }, [id, navigate, form]);
  
  // Handle expense deletion
  const handleDeleteExpense = async (id: string) => {
    if (!confirm("Are you sure you want to delete this expense?")) return;
    
    try {
      const { error } = await supabase
        .from("expenses")
        .delete()
        .eq("id", id);
      
      if (error) throw error;
      
      // Fix: Change toast object syntax to direct function call
      toast.success("The expense has been deleted successfully");
    } catch (error: any) {
      // Fix: Change toast object syntax to direct function call
      toast.error(error.message);
    }
  };

  // Function to handle expense approval
  const handleApprove = async () => {
    if (!id) return;
    
    try {
      // Fixed: Added third parameter (empty string for notes)
      const success = await updateExpenseStatus(id, "approved", "");
      if (success) {
        toast.success("Expense approved successfully");
        // Reload expense data and history
        const updatedExpense = await getExpenseById(id);
        setExpense(updatedExpense);
        const updatedHistory = await getExpenseHistory(id);
        setExpenseHistory(updatedHistory);
      } else {
        toast.error("Failed to approve expense");
      }
    } catch (error) {
      console.error("Error approving expense:", error);
      toast.error("An error occurred while approving the expense");
    }
  };
  
  // Function to handle expense rejection with comment
  const handleReject = async () => {
    if (!id || !Comment.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }
    
    setSubmitting(true);
    
    try {
      // Add rejection comment to expense history
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
         await updateExpenseStatus(id!, "rejected", Comment);
      }
      
      toast.success("Expense rejected");
      setIsRejectDialogOpen(false);
      setComment("");
      
      // Reload expense data and history
      const updatedExpense = await getExpenseById(id);
      setExpense(updatedExpense);
      const updatedHistory = await getExpenseHistory(id);
      setExpenseHistory(updatedHistory);
    } catch (error) {
      console.error("Error rejecting expense:", error);
      toast.error("An error occurred while rejecting the expense");
    } finally {
      setSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Function to get status badge with appropriate styling
  const getStatusBadge = (status: string) => {
    if (!status) return null;
    
    const statusColors = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-blue-100 text-blue-800",
      approved: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
      paid: "bg-purple-100 text-purple-800",
    };
    
    const color = statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800";
    
    return (
      <Badge variant="outline" className={color}>
        {status}
      </Badge>
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // If no expense found, show error
  if (!expense) {
    return (
      <div className="text-center py-8">
        <h1 className="text-2xl font-bold text-red-500">Expense Not Found</h1>
        <p className="mt-2">The expense you're looking for doesn't exist or you don't have permission to view it.</p>
        <Button onClick={() => navigate("/expenses")} className="mt-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Expenses
        </Button>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => navigate("/expenses")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <div className="flex space-x-2">
          {(expense.status === "draft" || expense.status === "rejected") && (
            <>
              <Button 
                onClick={() => navigate(`/expenses/edit/${id}`)}
                size="sm"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
              <Button 
                onClick={() => handleDeleteExpense(expense.id)}
                size="sm"
              >
                <Edit className="mr-2 h-4 w-4" />
                Delete
              </Button>
              <Button 
                variant="default"
                size="sm"
                onClick={async () => {
                  try {
                    // Fixed: Added third parameter (empty string for notes)
                    await updateExpenseStatus(id!, "submitted", "");
                    toast.success("Expense submitted for approval");
                    // Refresh expense data
                    const updatedExpense = await getExpenseById(id!);
                    setExpense(updatedExpense);
                    const updatedHistory = await getExpenseHistory(id!);
                    setExpenseHistory(updatedHistory);
                  } catch (error) {
                    console.error("Error submitting expense:", error);
                    toast.error("Failed to submit expense");
                  }
                }}
              >
                <Send className="mr-2 h-4 w-4" />
                Submit for Approval
              </Button>
            </>
          )}
          
          {expense.status === "submitted" && (
            <div className="flex space-x-2">
              <Button
                variant="default"
                size="sm"
                className="bg-green-600 hover:bg-green-700"
                onClick={handleApprove}
              >
                <Check className="mr-2 h-4 w-4" />
                Approve
              </Button>
              
              <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="destructive"
                    size="sm"
                  >
                    <X className="mr-2 h-4 w-4" />
                    Reject
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Provide Rejection Reason</DialogTitle>
                  </DialogHeader>
                  <div className="py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      Please provide a reason for rejecting this expense. This will be visible to the submitter.
                    </p>
                    <Textarea
                      placeholder="Enter rejection reason..."
                      value={Comment}
                      onChange={(e) => setComment(e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)} disabled={submitting}>
                      Cancel
                    </Button>
                    <Button 
                      variant="destructive" 
                      onClick={handleReject}
                      disabled={!Comment.trim() || submitting}
                    >
                      {submitting ? "Rejecting..." : "Reject Expense"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          )}
        </div>
      </div>

      <h1 className="text-3xl font-bold">{expense.title}</h1>
      
      {/* Expense Status Section */}
      <Card>
        <CardHeader>
          <CardTitle>Expense Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <div className="mt-1">{getStatusBadge(expense.status)}</div>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Date</p>
              <p className="mt-1">{new Date(expense.date).toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
              <p className="mt-1 font-semibold">{expense.currency} {expense.amount}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Created By</p>
              <p className="mt-1">{expense.created_by}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Description Section */}
      <Card>
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-700 whitespace-pre-wrap">{expense.description || "No description provided."}</p>
        </CardContent>
      </Card>
      
      {/* Line Items Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Line Items</h2>
        <FormProvider {...form}>
          <div className="space-y-4">
            {form.watch("lineItems").map((_, index) => (
              <ExpenseLineItem
                key={index}
                index={index}
                categories={categories}
                onRemove={() => {}} // No-op in view mode
                isRemoveDisabled={true}
                lineItemId=""
                isViewMode={true} // Set to view mode
              />
            ))}
          </div>
        </FormProvider>
      </div>
      
      {/* Expense History Section */}
      {expenseHistory.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Expense History
          </h2>
          <Card className="mt-4">
            <CardContent className="pt-6">
              <div className="space-y-4">
                {expenseHistory.map((entry) => (
                  <div key={entry.id} className="border-b pb-3 last:border-0">
                    <div className="flex justify-between items-center mb-1">
                      <div className="font-medium">
                        {entry.action === "status_change" ? (
                          <div className="flex items-center gap-2">
                            <span>Status changed from </span>
                            <Badge variant="outline">{entry.old_status || 'N/A'}</Badge>
                            <span> to </span>
                            <Badge variant="outline">{entry.new_status || 'N/A'}</Badge>
                          </div>
                        )  : (
                          entry.action
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {formatDate(entry.created_at)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      By {entry.profiles?.first_name || 'Unknown'} {entry.profiles?.last_name || 'User'}
                    </div>
                    
                    {/* Display rejection comments if available */}
                    {entry.notes && (
                      <div className="mt-2 text-sm bg-gray-50 p-2 rounded">
                        <span className="font-semibold">Comment:</span> {entry.notes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
