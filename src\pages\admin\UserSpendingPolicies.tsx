
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { getAllUserProfiles, getSpendingGroupPolicies, setUserSpendingPolicy } from "@/integrations/supabase/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define interfaces for user profiles and spending policies
interface UserProfile {
  id: string;
  first_name?: string;
  last_name?: string;
  email: string;
  role: string;
}

interface SpendingPolicy {
  id: string;
  name: string;
  policy_text: string;
  threshold_amount?: number;
}

interface UserPolicyMapping {
  user_id: string;
  group_id: string;
}

export default function UserSpendingPolicies() {
  const navigate = useNavigate();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [policies, setPolicies] = useState<SpendingPolicy[]>([]);
  const [userPolicyMappings, setUserPolicyMappings] = useState<UserPolicyMapping[]>([]);
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [selectedPolicy, setSelectedPolicy] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  // Load all users and spending policies
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        // Load user profiles
        const userProfiles = await getAllUserProfiles();
        setUsers(userProfiles);

        // Load spending group policies
        const groupPolicies = await getSpendingGroupPolicies();
        setPolicies(groupPolicies);
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle saving the policy assignment
  const handleSavePolicy = async () => {
    if (!selectedUser) {
      toast.error("Please select a user");
      return;
    }

    if (!selectedPolicy) {
      toast.error("Please select a spending policy");
      return;
    }

    setIsLoading(true);
    try {
      // Save the policy assignment
      const success = await setUserSpendingPolicy(selectedUser, selectedPolicy);
      
      if (success) {
        toast.success("Spending policy assigned successfully");
        
        // Update local state to reflect changes
        setUserPolicyMappings(prev => {
          const newMappings = [...prev];
          const existingIndex = newMappings.findIndex(m => m.user_id === selectedUser);
          
          if (existingIndex >= 0) {
            // Update existing mapping
            newMappings[existingIndex]!.group_id = selectedPolicy;
          } else {
            // Add new mapping
            newMappings.push({
              user_id: selectedUser,
              group_id: selectedPolicy
            });
          }
          
          return newMappings;
        });
      }
    } catch (error) {
      console.error("Error saving policy assignment:", error);
      toast.error("Failed to save policy assignment");
    } finally {
      setIsLoading(false);
    }
  };

  // Get policy details for a user
  // const getUserPolicy = (userId: string) => {
  //   const mapping = userPolicyMappings.find(m => m.user_id === userId);
  //   if (!mapping) return null;
  //
  //   return policies.find(p => p.id === mapping.group_id);
  // };

  // Display a list of users with their policies for quick editing
  const renderPoliciesList = () => {
    if (policies.length === 0) {
      return (
        <p className="text-muted-foreground">No spending policies have been set up yet.</p>
      );
    }

    return policies.map(policy => (
      <Card key={policy.id} className="mb-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">
            {policy.name}
            {policy.threshold_amount && (
              <span className="text-sm font-normal text-muted-foreground ml-2">
                (Threshold: {policy.threshold_amount})
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="whitespace-pre-wrap text-sm mb-4">{policy.policy_text}</p>
          
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Assigned Users:</h4>
            {userPolicyMappings.filter(m => m.group_id === policy.id).length > 0 ? (
              userPolicyMappings
                .filter(m => m.group_id === policy.id)
                .map(mapping => {
                  const user = users.find(u => u.id === mapping.user_id);
                  return (
                    <div key={mapping.user_id} className="text-sm text-muted-foreground">
                      {user ? `${user.first_name || ''} ${user.last_name || ''} (${user.email})` : 'Unknown user'}
                    </div>
                  );
                })
            ) : (
              <div className="text-sm text-muted-foreground">No users assigned</div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => {
              setSelectedPolicy(policy.id);
            }}
          >
            Select Policy
          </Button>
        </CardFooter>
      </Card>
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">User Spending Policies</h1>
        <Button onClick={() => navigate("/admin/dashboard")}>
          Back to Dashboard
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Assign Spending Policy</CardTitle>
            <CardDescription>
              Assign spending policies to users by selecting a user and a policy group.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">User</label>
              <Select
                value={selectedUser}
                onValueChange={setSelectedUser}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a user" />
                </SelectTrigger>
                <SelectContent>
                  {users.map(user => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.first_name || ''} {user.last_name || ''} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Policy Group</label>
              <Select
                value={selectedPolicy}
                onValueChange={setSelectedPolicy}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a policy group" />
                </SelectTrigger>
                <SelectContent>
                  {policies.map(policy => (
                    <SelectItem key={policy.id} value={policy.id}>
                      {policy.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={handleSavePolicy} 
              disabled={isLoading || !selectedUser || !selectedPolicy}
            >
              {isLoading ? "Saving..." : "Assign Policy"}
            </Button>
          </CardFooter>
        </Card>

        <div>
          <h2 className="text-xl font-semibold mb-4">Available Policies</h2>
          {renderPoliciesList()}
        </div>
      </div>
    </div>
  );
}
