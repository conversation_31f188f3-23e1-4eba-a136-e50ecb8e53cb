
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { StatusBadge } from "./StatusBadge";
import { PriorityBadge } from "./PriorityBadge";
import { Ticket } from "@/types";
import { format } from "date-fns";
import { ArrowDown, ArrowUp } from "lucide-react"; // Import sorting icons

// Define sorting types
type SortField = 'title' | 'status' | 'priority' | 'created_by' | 'department' | 'created_at';
type SortDirection = 'asc' | 'desc';

interface TicketTableProps {
  tickets: Ticket[];
  isLoading?: boolean;
  userRole?: "admin" | "agent" | "user";
  // Add sorting props
  sortField?: SortField;
  sortDirection?: SortDirection;
  onSort?: (field: SortField) => void;
}

export const TicketTable = ({ 
  tickets, 
  isLoading = false,
  userRole: _ = "user",
  sortField,
  sortDirection,
  onSort
}: TicketTableProps) => {
  const navigate = useNavigate();

  const handleRowClick = (ticketId: string) => {
    navigate(`/tickets/${ticketId}`);
  };

  // Function to render sort indicator
  const renderSortIndicator = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' ? (
        <ArrowUp className="h-3 w-3 ml-1 inline" />
      ) : (
        <ArrowDown className="h-3 w-3 ml-1 inline" />
      );
    }
    return null;
  };

  // Function to handle header click for sorting
  const handleHeaderClick = (field: SortField) => {
    if (onSort) {
      onSort(field);
    }
  };

  if (isLoading) {
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title & Description</TableHead>
            <TableHead className="hidden md:table-cell">Status</TableHead>
            <TableHead className="hidden md:table-cell">Priority</TableHead>
            <TableHead className="hidden md:table-cell">Created By</TableHead>
            <TableHead className="hidden md:table-cell">Department</TableHead>
            <TableHead className="hidden md:table-cell">Created</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell colSpan={6} className="h-24 text-center">
              Loading tickets...
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );
  }

  if (tickets.length === 0) {
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title & Description</TableHead>
            <TableHead className="hidden md:table-cell">Status</TableHead>
            <TableHead className="hidden md:table-cell">Priority</TableHead>
            <TableHead className="hidden md:table-cell">Created By</TableHead>
            <TableHead className="hidden md:table-cell">Department</TableHead>
            <TableHead className="hidden md:table-cell">Created</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell colSpan={6} className="h-24 text-center">
              No tickets found.
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          {/* Make column headers clickable for sorting */}
          <TableHead 
            className="cursor-pointer hover:bg-accent/50"
            onClick={() => handleHeaderClick('title')}
          >
            Title & Description {renderSortIndicator('title')}
          </TableHead>
          <TableHead 
            className="hidden md:table-cell cursor-pointer hover:bg-accent/50"
            onClick={() => handleHeaderClick('status')}
          >
            Status {renderSortIndicator('status')}
          </TableHead>
          <TableHead 
            className="hidden md:table-cell cursor-pointer hover:bg-accent/50"
            onClick={() => handleHeaderClick('priority')}
          >
            Priority {renderSortIndicator('priority')}
          </TableHead>
          <TableHead 
            className="hidden md:table-cell cursor-pointer hover:bg-accent/50"
            onClick={() => handleHeaderClick('created_by')}
          >
            Created By {renderSortIndicator('created_by')}
          </TableHead>
          <TableHead 
            className="hidden md:table-cell cursor-pointer hover:bg-accent/50"
            onClick={() => handleHeaderClick('department')}
          >
            Department {renderSortIndicator('department')}
          </TableHead>
          <TableHead 
            className="hidden md:table-cell cursor-pointer hover:bg-accent/50"
            onClick={() => handleHeaderClick('created_at')}
          >
            Created {renderSortIndicator('created_at')}
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {tickets.map((ticket) => (
          <TableRow 
            key={ticket.id} 
            className="cursor-pointer hover:bg-muted"
            onClick={() => handleRowClick(ticket.id)}
          >
            <TableCell>
              <div>
                <div className="font-medium">{ticket.title}</div>
                <div className="text-sm text-muted-foreground mt-1">{ticket.description}</div>
                <div className="md:hidden mt-1 flex flex-wrap gap-2">
                  <StatusBadge status={ticket.status} />
                  <PriorityBadge priority={ticket.priority} />
                  <span className="text-xs text-muted-foreground">By: {ticket.created_by_name}</span>
                  <span className="text-xs text-muted-foreground">Dept: {ticket.department_name}</span>
                </div>
              </div>
            </TableCell>
            <TableCell className="hidden md:table-cell">
              <StatusBadge status={ticket.status} />
            </TableCell>
            <TableCell className="hidden md:table-cell">
              <PriorityBadge priority={ticket.priority} />
            </TableCell>
            <TableCell className="hidden md:table-cell">
              {ticket.created_by_name || "Unknown"}
            </TableCell>
            <TableCell className="hidden md:table-cell">
              {ticket.department_name || "Unassigned"}
            </TableCell>
            <TableCell className="hidden md:table-cell">
              {format(new Date(ticket.created_at), "MMM d, yyyy")}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
