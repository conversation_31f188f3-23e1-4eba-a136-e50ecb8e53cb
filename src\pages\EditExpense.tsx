
import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { ExpenseForm } from "@/components/expense/ExpenseForm";
import { getExpenseById, getExpenseHistory } from "@/integrations/supabase/api";
import { Loader2, Clock } from "lucide-react";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Separator } from "@/components/ui/separator";
// import { Badge } from "@/components/ui/badge";

// Define the ExpenseHistory interface
interface ExpenseHistory {
  id: string;
  expense_id: string;
  action: string;
  old_status: string | null;
  new_status: string | null;
  notes: string | null; // Changed from Comment to notes
  user_id: string;
  created_at: string;
  profiles?: {
    first_name?: string;
    last_name?: string;
  };
}

const EditExpense = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [expenseData, setExpenseData] = useState(null);
  const [expenseHistory, setExpenseHistory] = useState<ExpenseHistory[]>([]);
  
  useEffect(() => {
    // If we have an ID, fetch the expense data with line items
    if (id) {
      const loadExpense = async () => {
        setLoading(true);
        try {
          // Use the API function to get expense with line items
          const data = await getExpenseById(id);
          console.log("Loaded expense data:", data);
          
          if (!data) {
            // If no expense was found, show an error and redirect
            toast.error("Expense not found or you don't have permission to view it");
            navigate("/expenses");
            return;
          }
          
          setExpenseData(data);
          
          // Load expense history
          const history = await getExpenseHistory(id);
          console.log("Loaded expense history:", history);
          setExpenseHistory(history);
          
        } catch (error) {
          console.error("Error loading expense:", error);
          toast.error("Failed to load expense details");
          // Redirect to expenses list on error
          navigate("/expenses");
        } finally {
          setLoading(false);
        }
      };
      
      loadExpense();
    }
  }, [id, navigate]);

  // Format the date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };
  
  // Function to get status badge with appropriate styling
  const getStatusBadge = (status: string | null) => {
    if (!status) return null;
    
    const statusColors: Record<string, string> = {
      draft: "bg-gray-100 text-gray-800",
      submitted: "bg-blue-100 text-blue-800",
      approved: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
      paid: "bg-purple-100 text-purple-800",
    };
    
    const color = statusColors[status] || "bg-gray-100 text-gray-800";
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs ${color}`}>
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <h1 className="text-3xl font-bold">Edit Expense</h1>
      <ExpenseForm initialData={expenseData || undefined} />
      
      {/* Expense History Section */}
      {expenseHistory.length > 0 && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold flex items-center gap-2 mb-4">
            <Clock className="h-5 w-5" />
            Expense History
          </h2>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Workflow History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {expenseHistory.map((entry) => (
                  <div key={entry.id} className="border-b pb-3 last:border-0">
                    <div className="flex justify-between items-center mb-1">
                      <div className="font-medium">
                        {entry.action === "status_change" ? (
                          <div className="flex items-center gap-2">
                            <span>Status changed from</span>
                            {getStatusBadge(entry.old_status)}
                            <span>to</span>
                            {getStatusBadge(entry.new_status)}
                          </div>
                        )  : (
                          entry.action
                        )}
                      </div>
                      <span className="text-sm text-gray-500">
                        {formatDate(entry.created_at)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      By {entry.profiles?.first_name || 'Unknown'} {entry.profiles?.last_name || 'User'}
                    </div>
                    
                    {/* Display rejection comments if available */}
                    {entry.notes && (
                      <div className="mt-2 text-sm bg-gray-50 p-2 rounded">
                        <span className="font-semibold">Comment:</span> {entry.notes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EditExpense;
