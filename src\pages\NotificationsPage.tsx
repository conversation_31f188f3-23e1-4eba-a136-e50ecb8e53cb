
import { useState, useEffect } from "react";
import { Bell, CheckCircle, Clock, AlertCircle, Filter } from "lucide-react";
import { toast } from "sonner";
import { genericSupabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Define filter types for notifications
type NotificationFilter = 'all' | 'myTickets' | 'assignedToMe';

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeFilter, setActiveFilter] = useState<NotificationFilter>('myTickets'); // Default to user's tickets
  const { user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchNotifications = async () => {
      if (!user) return;
      
      try {
        setIsLoading(true);
        
        // First get user role to determine what notifications to show
        const { data: userData, error: userError } = await genericSupabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();
          
        if (userError) {
          console.error("Error fetching user role:", userError);
          return;
        }
        
        const userRole = userData?.role;
        
        // Get user departments if they're an agent
        // let userDepartments: string[] = [];
        if (userRole === 'agent') {
          const { data: deptData, error: deptError } = await genericSupabase
            .from('user_departments')
            .select('department_id')
            .eq('user_id', user.id);
            
          if (!deptError && deptData) {
            userDepartments = deptData.map(d => d.department_id);
          }
        }
        
        // Build base query
        let query = genericSupabase
          .from('ticket_activity')
          .select(`
            *,
            tickets!inner(id, title, created_by, assigned_to, department_id),
            users(full_name)
          `)
          .order('created_at', { ascending: false });
        
        // Apply filter based on selected option and user role
        
        if (activeFilter === 'myTickets') {
          
          // Show activities for tickets created by the user
          query = query.filter('tickets.created_by', 'eq', user.id);
        } else if (activeFilter === 'assignedToMe') {
         
          // Show activities for tickets assigned to the user
          query = query.filter('tickets.assigned_to', 'eq', user.id);
        } else if (activeFilter === 'all') {
         
          // For 'all' filter, still respect user role permissions
          if (userRole === 'admin' || userRole === 'agent') {
            // Admins see all notifications, no filter needed
          }  else {
            
            // Regular users only see notifications for tickets they created
            query = query.eq('tickets.created_by', user.id);
          }
        }
        
        const { data, error } = await query;
        
        if (error) {
          console.error("Error fetching notifications:", error);
          throw error;
        }
        
        console.log("Filtered notifications:", data);
        setNotifications(data || []);
      } catch (error) {
        console.error("Error fetching notifications:", error);
        toast.error("Failed to load notifications");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchNotifications();
    

  }, [user, activeFilter]);

  const getNotificationIcon = (activityType: string) => {
    switch (activityType) {
      case 'status_change':
      case 'resolved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'created':
      case 'comment':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'assigned':
        return <Bell className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const formatNotification = (notification: any) => {
    const { activity_type, details, tickets, users } = notification;
    const ticketTitle = tickets?.title || "Unknown ticket";
    const userName = users?.full_name || "Someone";

    switch (activity_type) {
      case 'status_change':
        return `${userName} changed status of "${ticketTitle}" to ${details}`;
      case 'comment':
        return `${userName} commented on "${ticketTitle}"`;
      case 'created':
        return `${userName} created ticket "${ticketTitle}"`;
      case 'assigned':
        return `${userName} assigned ticket "${ticketTitle}" to ${details}`;
      case 'priority_change':
        return `${userName} changed priority of "${ticketTitle}" to ${details}`;
      case 'department_change':
        return `${userName} moved "${ticketTitle}" to ${details} department`;
      default:
        return `${userName} updated "${ticketTitle}"`;
    }
  };

  const navigateToTicket = (ticketId: string) => {
    if (ticketId) {
      navigate(`/tickets/${ticketId}`);
    }
  };

  const getFilterLabel = (filter: NotificationFilter) => {
    switch (filter) {
      case 'all':
        return 'All Activity';
      case 'myTickets':
        return 'My Tickets';
      case 'assignedToMe':
        return 'Assigned To Me';
      default:
        return 'Notifications';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">Notifications</h1>
          <p className="text-muted-foreground">
            View activity updates for your tickets
          </p>
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <Filter className="h-4 w-4 mr-1" />
              {getFilterLabel(activeFilter)}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Filter By</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className={activeFilter === 'myTickets' ? 'bg-accent' : ''}
              onClick={() => setActiveFilter('myTickets')}
            >
              My Tickets
            </DropdownMenuItem>
            <DropdownMenuItem
              className={activeFilter === 'assignedToMe' ? 'bg-accent' : ''}
              onClick={() => setActiveFilter('assignedToMe')}
            >
              Assigned To Me
            </DropdownMenuItem>
            <DropdownMenuItem
              className={activeFilter === 'all' ? 'bg-accent' : ''}
              onClick={() => setActiveFilter('all')}
            >
              All Activity
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      <Separator className="my-6" />
      
      <div className="space-y-4">
        {isLoading ? (
          Array(5).fill(0).map((_, i) => (
            <Card key={i} className="bg-card">
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : notifications.length > 0 ? (
          notifications.map((notification) => (
            <Card 
              key={notification.id} 
              className="bg-card hover:bg-accent/50 cursor-pointer transition-colors"
              onClick={() => navigateToTicket(notification.ticket_id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  <div className="mt-1">
                    {getNotificationIcon(notification.activity_type)}
                  </div>
                  <div className="space-y-1 flex-1">
                    <p className="text-sm font-medium">{formatNotification(notification)}</p>
                    <div className="flex flex-wrap items-center gap-2">
                      <p className="text-xs text-muted-foreground">
                        {new Date(notification.created_at).toLocaleString()}
                      </p>
                      {activeFilter === 'all' && (
                        <Badge variant="outline" className="text-xs">
                          {notification.tickets?.created_by === user?.id ? 'My Ticket' : 
                            notification.tickets?.assigned_to === user?.id ? 'Assigned to Me' : 'Other'}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-10">
            <Bell className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium">No notifications</h3>
            <p className="text-muted-foreground mt-2">
              {activeFilter === 'myTickets' 
                ? "No activity on tickets you've created" 
                : activeFilter === 'assignedToMe'
                ? "No activity on tickets assigned to you"
                : "No activity to display"}
            </p>
            <Button 
              variant="outline" 
              className="mt-4" 
              onClick={() => navigate("/tickets")}
            >
              Go to Tickets
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotificationsPage;
