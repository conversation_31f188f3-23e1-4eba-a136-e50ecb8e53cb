import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
// import { StatusBadge } from "@/components/ticket/StatusBadge";
// import { PriorityBadge } from "@/components/ticket/PriorityBadge";
import { format } from "date-fns";
import { Ticket, User, Comment, Tag, TicketPriority, TicketAttachment, Department, TicketActivity as TicketActivity } from "@/types";
import { ArrowLeft, MessageSquare, Tag as TagIcon, Paperclip, Upload, X, File, History } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { genericSupabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { TicketHistory } from "./TicketHistory";
import { CommentTypeButtons } from "./CommentTypeButtons";
import { CommentTypeBadge } from "./CommentTypeBadge";
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Bold from '@tiptap/extension-bold';
import Italic from '@tiptap/extension-italic';
import Underline from '@tiptap/extension-underline';
import Heading from '@tiptap/extension-heading';
import { TicketActivityService } from "@/services/TicketActivityService";

interface TicketDetailsProps {
  ticket: Ticket;
  comments: Comment[];
  agents: User[];
  tags?: Tag[];
  currentUser?: User;
  ticketCreator?: User;
  attachments?: TicketAttachment[];
  onStatusChange: (ticketId: string, status: string) => void;
  onAssignChange: (ticketId: string, userId: string) => void;
  onPriorityChange: (ticketId: string, priority: TicketPriority) => void;
  onAddComment?: (ticketId: string, content: string) => void;
  onFileUpload?: (ticketId: string, files: FileList) => Promise<void>;
  onFileDelete?: (attachmentId: string) => Promise<void>;
}

export function TicketDetails({ 
  ticket, 
  comments, 
  agents,
  tags = [],
  currentUser,
  ticketCreator,
  attachments = [],
  onStatusChange,
  onAssignChange,
  onPriorityChange,
  onAddComment,
  onFileUpload,
  onFileDelete
}: TicketDetailsProps) {
  const navigate = useNavigate();
  const [newComment, setNewComment] = useState("");
  const [isTagDialogOpen, setIsTagDialogOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [isSubmittingTags, setIsSubmittingTags] = useState(false);
  const [displayTags, setDisplayTags] = useState<Tag[]>([]);
  const [selectedStatus, setSelectedStatus] = useState(ticket.status);
  const [selectedAssignee, setSelectedAssignee] = useState(ticket.assigned_to || "");
  const [selectedPriority, setSelectedPriority] = useState(ticket.priority);
  const [uploadingFiles, setUploadingFiles] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState(ticket.department || "");
  const [departmentAgents, setDepartmentAgents] = useState<User[]>([]);
  const [ticketActivities, setTicketActivities] = useState<TicketActivity[]>([]);
  const [userMap, setUserMap] = useState<Record<string, { full_name: string; avatar_url?: string }>>({});
  const [hasChanges, setHasChanges] = useState(false);
  
  // TipTap Rich Text Editor setup
  const editor = useEditor({
    extensions: [
      StarterKit,
      Bold,
      Italic,
      Underline,
      Heading.configure({
        levels: [1, 2, 3],
      }),
    ],
    content: '',
    onUpdate: ({ editor }) => {
      setNewComment(editor.getHTML());
    },
  });

  // Fetch ticket activities
  useEffect(() => {
    const fetchTicketActivities = async () => {
      if (!ticket.id) return;
      
      try {
        const { data, error } = await genericSupabase
          .from('ticket_activity')
          .select('*')
          .eq('ticket_id', ticket.id)
          .order('created_at', { ascending: false });
          
        if (error) {
          console.error("Error fetching ticket activities:", error);
          return;
        }
        
        setTicketActivities(data || []);
        
        // Build a map of user IDs to fetch user data efficiently
        const userIds = new Set<string>();
        data?.forEach(activity => {
          if (activity.user_id) userIds.add(activity.user_id);
        });
        
        if (userIds.size > 0) {
          const { data: userData, error: userError } = await genericSupabase
            .from('users')
            .select('id, full_name, avatar_url')
            .in('id', Array.from(userIds));
            
          if (!userError && userData) {
            const userMapObj: Record<string, { full_name: string; avatar_url?: string }> = {};
            userData.forEach(user => {
              userMapObj[user.id] = { full_name: user.full_name, avatar_url: user.avatar_url };
            });
            setUserMap(userMapObj);
          }
        }
      } catch (error) {
        console.error("Error in fetchTicketActivities:", error);
      }
    };
    
    fetchTicketActivities();
  }, [ticket.id]);
  
  // Fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const { data, error } = await genericSupabase
          .from('departments')
          .select('*')
          .order('name');
          
        if (error) {
          console.error("Error fetching departments:", error);
          return;
        }
        
        setDepartments(data || []);
      } catch (error) {
        console.error("Error in fetchDepartments:", error);
      }
    };
    
    fetchDepartments();
  }, []);
  
  // Filter agents by selected department
  useEffect(() => {
    const filterAgentsByDepartment = async () => {
      if (!selectedDepartment) {
        setDepartmentAgents(agents);
        return;
      }
      
      try {
        // First get all users who belong to the selected department
        const { data: userDepartmentsData, error: userDepartmentsError } = await genericSupabase
          .from('user_departments')
          .select('user_id')
          .eq('department_id', selectedDepartment);
          
        if (userDepartmentsError) {
          console.error("Error fetching user departments:", userDepartmentsError);
          return;
        }
        
        if (!userDepartmentsData || userDepartmentsData.length === 0) {
          setDepartmentAgents([]);
          return;
        }
        
        // Get the user IDs of users in this department
        const userIds = userDepartmentsData.map(ud => ud.user_id);
        
        // Filter agents that belong to this department
        const departmentAgents = agents.filter(agent => userIds.includes(agent.id));
        setDepartmentAgents(departmentAgents);
        
        // If the currently selected assignee is not in this department, reset it
        if (selectedAssignee && !userIds.includes(selectedAssignee)) {
          setSelectedAssignee("");
          setHasChanges(true);
        }
      } catch (error) {
        console.error("Error filtering agents by department:", error);
      }
    };
    
    filterAgentsByDepartment();
  }, [selectedDepartment, agents]);
  
  // Check for changes in the form fields
  useEffect(() => {
    const hasStatusChanged = selectedStatus !== ticket.status;
    const hasAssigneeChanged = selectedAssignee !== (ticket.assigned_to || "");
    const hasPriorityChanged = selectedPriority !== ticket.priority;
    const hasDepartmentChanged = selectedDepartment !== (ticket.department || "");
    
    setHasChanges(hasStatusChanged || hasAssigneeChanged || hasPriorityChanged || hasDepartmentChanged);
  }, [selectedStatus, selectedAssignee, selectedPriority, selectedDepartment, ticket]);
  
  // Fetch tags for this ticket
  useEffect(() => {
    const fetchTicketTags = async () => {
      if (!ticket.id) return;
      
      try {
        // Get tag IDs linked to this ticket
        const { data: ticketTagsData, error: ticketTagsError } = await genericSupabase
          .from('ticket_tags')
          .select('tag_id')
          .eq('ticket_id', ticket.id);
          
        if (ticketTagsError) {
          console.error("Error fetching ticket tags:", ticketTagsError);
          return;
        }
        
        if (!ticketTagsData || ticketTagsData.length === 0) {
          setDisplayTags([]);
          setSelectedTags([]);
          return;
        }
        
        // Get tag IDs
        const tagIds = ticketTagsData.map(tt => tt.tag_id);
        setSelectedTags(tagIds);
        
        // Fetch full tag information
        const { data: tagData, error: tagError } = await genericSupabase
          .from('tags')
          .select('*')
          .in('id', tagIds);
          
        if (tagError) {
          console.error("Error fetching tags:", tagError);
          return;
        }
        
        setDisplayTags(tagData || []);
      } catch (error) {
        console.error("Error in fetchTicketTags:", error);
      }
    };
    
    fetchTicketTags();
  }, [ticket.id]);
  
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };
  
  const findUserById = (userId: string) => {
    return agents.find((user) => user.id === userId);
  };

  const handleCommentSubmit = async (commentType: 'internal' | 'customer' | 'resolving') => {
    if (!newComment.trim() && !hasChanges) return;
    
    setIsSubmittingComment(true);
    try {
      // If we have comment content, add it
      if (newComment.trim()) {
        // Auto-determine comment type for regular users
        const finalCommentType = currentUser?.role === 'user' ? 'customer' : commentType;
        
        // Create the comment with the specified type
        const { error: commentError } = await genericSupabase
          .from('ticket_comments')
          .insert({
            ticket_id: ticket.id,
            content: newComment,
            user_id: currentUser?.id || ticket.created_by,
            comment_type: finalCommentType,
            is_internal: finalCommentType === 'internal'
          })
          .select();
        
        if (commentError) throw commentError;
        
        // Log comment activity
        if (currentUser?.id) {
          await TicketActivityService.logComment(ticket.id, currentUser.id);
        }
      }
      
      // Apply status change if it was modified
      if (selectedStatus !== ticket.status) {
        await onStatusChange(ticket.id, selectedStatus);
      }
      
      // Apply department change if it was modified
      if (selectedDepartment !== (ticket.department || "")) {
        try {
          const departmentName = departments.find(d => d.id === selectedDepartment)?.name || "Unassigned";
          
          const { error: departmentUpdateError } = await genericSupabase
            .from('tickets')
            .update({ 
              department_id: selectedDepartment || null, 
              updated_at: new Date().toISOString() 
            })
            .eq('id', ticket.id);
          
          if (departmentUpdateError) throw departmentUpdateError;
          
          // Log department change activity
          if (currentUser?.id) {
            await TicketActivityService.logDepartmentChange(ticket.id, currentUser.id, departmentName);
          }
        } catch (error) {
          console.error("Error updating department:", error);
          toast.error("Failed to update department");
        }
      }
      
      // Apply assignee change if it was modified
      if (selectedAssignee !== (ticket.assigned_to || "")) {
        await onAssignChange(ticket.id, selectedAssignee);
      }
      
      // Apply priority change if it was modified
      if (selectedPriority !== ticket.priority) {
        await onPriorityChange(ticket.id, selectedPriority);
      }
      
      // Call the callback function if provided and we have a comment
      if (onAddComment && newComment.trim()) {
        onAddComment(ticket.id, newComment);
      }
      
      if (editor) {
        editor.commands.clearContent();
      }
      setNewComment("");
      
      // Show different success messages based on comment type
      if (commentType === 'resolving') {
        toast.success("Closing comment added and ticket resolved");
      } else if (commentType === 'internal') {
        toast.success("Internal comment added successfully");
      } else {
        toast.success(newComment.trim() ? "Comment added successfully" : "Ticket updated successfully");
      }
      
      // Refresh the page to show the new data
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Error adding comment or updating ticket:", error);
      toast.error("Failed to update ticket");
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const handleTagSelection = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId) 
        : [...prev, tagId]
    );
  };

  const handleSaveTags = async () => {
    setIsSubmittingTags(true);
    try {
      // First, delete existing ticket-tag associations
      const { error: deleteError } = await genericSupabase
        .from('ticket_tags')
        .delete()
        .eq('ticket_id', ticket.id);
        
      if (deleteError) throw deleteError;
      
      // Then insert new associations
      if (selectedTags.length > 0) {
        const tagsToInsert = selectedTags.map(tagId => ({
          ticket_id: ticket.id,
          tag_id: tagId
        }));
        
        const { error: insertError } = await genericSupabase
          .from('ticket_tags')
          .insert(tagsToInsert);
          
        if (insertError) throw insertError;
      }
      
      toast.success("Tags updated successfully");
      setIsTagDialogOpen(false);
      
      // Refresh the page to show the updated tags
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error("Error updating tags:", error);
      toast.error("Failed to update tags");
    } finally {
      setIsSubmittingTags(false);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;
    
    if (onFileUpload) {
      setUploadingFiles(true);
      setUploadProgress(10); // Start progress
      
      try {
        // Simulate progress during upload
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => Math.min(prev + 10, 90));
        }, 300);
        
        await onFileUpload(ticket.id, e.target.files);
        
        clearInterval(progressInterval);
        setUploadProgress(100);
        
        setTimeout(() => {
          toast.success("Files uploaded successfully");
          setUploadingFiles(false);
          setUploadProgress(0);
        }, 500);
      } catch (error) {
        console.error("Error uploading files:", error);
        toast.error("Failed to upload files");
        setUploadingFiles(false);
        setUploadProgress(0);
      }
    }
  };
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };
  
  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };
  
  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (!e.dataTransfer.files || e.dataTransfer.files.length === 0) return;
    
    if (onFileUpload) {
      setUploadingFiles(true);
      setUploadProgress(10); // Start progress
      
      try {
        // Simulate progress during upload
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => Math.min(prev + 10, 90));
        }, 300);
        
        await onFileUpload(ticket.id, e.dataTransfer.files);
        
        clearInterval(progressInterval);
        setUploadProgress(100);
        
        setTimeout(() => {
          toast.success("Files uploaded successfully");
          setUploadingFiles(false);
          setUploadProgress(0);
        }, 500);
      } catch (error) {
        console.error("Error uploading files:", error);
        toast.error("Failed to upload files");
        setUploadingFiles(false);
        setUploadProgress(0);
      }
    }
  };
  
  const handleDeleteAttachment = async (attachmentId: string) => {
    if (onFileDelete) {
      try {
        await onFileDelete(attachmentId);
        toast.success("File deleted successfully");
      } catch (error) {
        console.error("Error deleting file:", error);
        toast.error("Failed to delete file");
      }
    }
  };
  
  const formatFileSize = (size: number): string => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  };

  // Find department name
  // const getDepartmentName = (departmentId: string | undefined) => {
  //   if (!departmentId) return "Unassigned";
  //   const department = departments.find(d => d.id === departmentId);
  //   return department ? department.name : "Unassigned";
  // };
  
  // Rich text editor toolbar
  const RichTextToolbar = () => (
    <div className="border border-input rounded-t-md p-1 flex flex-wrap gap-1 bg-muted/30">
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('bold') ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleBold().run()}
      >
        <span className="font-bold">B</span>
      </Button>
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('italic') ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleItalic().run()}
      >
        <span className="italic">I</span>
      </Button>
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('underline') ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleUnderline().run()}
      >
        <span className="underline">U</span>
      </Button>
      <Separator orientation="vertical" className="mx-1 h-8" />
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('bulletList') ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleBulletList().run()}
      >
        •
      </Button>
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('orderedList') ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleOrderedList().run()}
      >
        1.
      </Button>
      <Separator orientation="vertical" className="mx-1 h-8" />
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('heading', { level: 2 }) ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleHeading({ level: 2 }).run()}
      >
        H2
      </Button>
      <Button 
        type="button" 
        size="icon" 
        variant="ghost" 
        className={`h-8 w-8 ${editor?.isActive('heading', { level: 3 }) ? 'bg-muted' : ''}`}
        onClick={() => editor?.chain().focus().toggleHeading({ level: 3 }).run()}
      >
        H3
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => navigate(-1)}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsTagDialogOpen(true)}
          >
            <TagIcon className="mr-2 h-4 w-4" />
            Manage Tags
          </Button>
        </div>
      </div>
      
      <Card>
        <CardHeader>
                    {ticketCreator && (
            <div className="bg-secondary/20 rounded-lg p-3">
              <h3 className="font-medium mb-2">Created By</h3>
              <div className="flex items-center gap-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={ticketCreator.avatar_url} alt={ticketCreator.full_name} />
                  <AvatarFallback>{ticketCreator ? getInitials(ticketCreator.full_name) : "U"}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{ticketCreator.full_name}</p>
                  <p className="text-xs text-muted-foreground">{ticketCreator.email}</p>
                  {ticketCreator.company_name && (
                    <p className="text-xs text-muted-foreground">{ticketCreator.company_name}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          <Separator />

          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
            <div>
              <CardTitle className="text-2xl">{ticket.title}
             
                {displayTags.map((tag) => (
                  <Badge key={tag.id} variant="outline" className="bg-primary/10 text-primary border-primary/20">
                    {tag.name}
                  </Badge>
                ))}
              </CardTitle>
            </div>
            <div className="flex flex-col md:items-end gap-1 text-sm">

              
              <div className="text-muted-foreground">
                Created on {format(new Date(ticket.created_at), "MMM d, yyyy")}
              </div>
              <div className="text-muted-foreground">
                ID: {ticket.id.substring(0, 8)}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">

          
          <div>
            <h3 className="font-medium mb-2">Description</h3>
            <div className="text-sm whitespace-pre-line">{ticket.description}</div>
          </div>

                    <Separator />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium">Status</h3>
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as any)}
              >
                <option value="open">Open</option>
                <option value="in-progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">Department</h3>
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
              >
                <option value="">Unassigned</option>
                {departments.map((department) => (
                  <option key={department.id} value={department.id}>
                    {department.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">Priority</h3>
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={selectedPriority}
                onChange={(e) => setSelectedPriority(e.target.value as TicketPriority)}
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>
            
            <div className="space-y-2">
              <h3 className="font-medium">Assigned To</h3>
              <select
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                value={selectedAssignee}
                onChange={(e) => setSelectedAssignee(e.target.value)}
              >
                <option value="">Unassigned</option>
                {departmentAgents.length > 0 ? (
                  departmentAgents.map((agent) => (
                    <option key={agent.id} value={agent.id}>
                      {agent.full_name}
                    </option>
                  ))
                ) : (
                  <option disabled value="">No agents available for this department</option>
                )}
              </select>
              {selectedDepartment && departmentAgents.length === 0 && (
                <p className="text-xs text-amber-500 mt-1">
                  No agents are assigned to this department
                </p>
              )}
            </div>
          </div>
          
          {/* File attachments section */}
          <div>
            <h3 className="font-medium flex items-center mb-4">
              <Paperclip className="mr-2 h-4 w-4" />
              Attachments ({attachments.length})
            </h3>
            
            <div 
              className={`border-2 border-dashed rounded-lg p-4 mb-4 text-center ${dragActive ? 'border-primary bg-primary/5' : 'border-muted'}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input 
                type="file" 
                id="file-upload" 
                multiple 
                className="hidden" 
                onChange={handleFileUpload}
                disabled={uploadingFiles}
              />
              
              {uploadingFiles ? (
                <div className="py-4">
                  <p className="text-sm text-muted-foreground mb-2">Uploading files...</p>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              ) : (
                <>
                  <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground mb-1">
                    Drag and drop files here or click to browse
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => document.getElementById('file-upload')?.click()}
                  >
                    Upload Files
                  </Button>
                </>
              )}
            </div>
            
            {attachments.length > 0 && (
              <div className="space-y-2 max-h-[200px] overflow-y-auto p-2">
                {attachments.map((attachment) => (
                  <div key={attachment.id} className="flex items-center justify-between bg-secondary/10 p-2 rounded">
                    <div className="flex items-center">
                      <File className="h-4 w-4 mr-2 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium truncate max-w-[150px] md:max-w-[300px]">
                          {attachment.file_name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(attachment.file_size)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6"
                        onClick={() => window.open(attachment.file_path, '_blank')}
                      >
                        <ArrowLeft className="h-4 w-4 rotate-[-135deg]" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6 text-destructive hover:text-destructive/80"
                        onClick={() => handleDeleteAttachment(attachment.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <Separator />
          
          <div>

                        <div className="w-full">
              {/* Rich Text Editor */}
              <RichTextToolbar />
              <div className="border border-input border-t-0 rounded-b-md min-h-[150px] bg-background">
                <EditorContent 
                  editor={editor} 
                  className="prose prose-sm max-w-none p-3 min-h-[150px] focus:outline-none" 
                />
              </div>
              <div className="flex justify-between mt-2">
                {hasChanges && (
                  <p className="text-xs text-muted-foreground self-center">
                    Changes will be applied when you submit
                  </p>
                )}
                <div className="flex ml-auto">
                  <CommentTypeButtons
                    onAddComment={handleCommentSubmit}
                    isSubmitting={isSubmittingComment}
                    hasContent={newComment.trim().length > 0 || hasChanges}
                    userRole={currentUser?.role || 'user'}
                  />
                </div>
              </div>
            </div>

            
            <h3 className="font-medium flex items-center mb-4 mt-6">
              <MessageSquare className="mr-2 h-4 w-4" />
              Comments ({comments.length})
            </h3>
            
            <div className="space-y-4 max-h-[400px] overflow-y-auto scrollbar-hide mb-4">
              {comments.map((comment) => {
                const user = findUserById(comment.user_id);
                return (
                  <div key={comment.id} className="flex gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.avatar_url} alt={user?.full_name} />
                      <AvatarFallback>{user ? getInitials(user.full_name) : "U"}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">
                          {user?.full_name || "Unknown user"}
                        </span>
                        <CommentTypeBadge commentType={comment.comment_type} />
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(comment.created_at), "MMM d, yyyy 'at' h:mm a")}
                        </span>
                      </div>
                      <div 
                        className="text-sm mt-1"
                        dangerouslySetInnerHTML={{ __html: comment.content }}
                      />
                    </div>
                  </div>
                );
              })}
              
              {comments.length === 0 && (
                <div className="text-center py-8 text-muted-foreground text-sm">
                  No comments yet.
                </div>
              )}
            </div>
            

          </div>
          
          <Separator className="my-4" />
          
          {/* Ticket Activity History - Moved after comments section */}
          <div>
            <h3 className="font-medium flex items-center mb-4">
              <History className="mr-2 h-4 w-4" />
              Ticket History
            </h3>
            <TicketHistory activities={ticketActivities} userMap={userMap} />
          </div>
        </CardContent>
      </Card>

      {/* Tag Management Dialog */}
      <Dialog open={isTagDialogOpen} onOpenChange={setIsTagDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Manage Tags</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-4">
              Select tags to associate with this ticket.
            </p>
            <div className="space-y-2">
              {tags && tags.length > 0 ? (
                tags.map((tag) => (
                  <div key={tag.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`tag-${tag.id}`}
                      checked={selectedTags.includes(tag.id)}
                      onChange={() => handleTagSelection(tag.id)}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor={`tag-${tag.id}`} className="text-sm font-medium">
                      {tag.name}
                    </label>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">
                  No tags available. Create tags on the Tags page.
                </p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTagDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSaveTags} 
              disabled={isSubmittingTags}
            >
              {isSubmittingTags ? "Saving..." : "Save Tags"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
