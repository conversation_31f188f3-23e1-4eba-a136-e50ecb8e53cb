
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Plus, Edit, Trash, Search, FileText } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";

interface Expense {
  id: string;
  title: string;
  amount: number;
  currency: string;
  date: string;
  status: string;
  category_id: string | null;
  category_name?: string;
  created_at: string;
}

const statusColors: Record<string, string> = {
  draft: "bg-gray-200 text-gray-800",
  submitted: "bg-blue-100 text-blue-800",
  under_review: "bg-yellow-100 text-yellow-800",
  approved: "bg-green-100 text-green-800",
  rejected: "bg-red-100 text-red-800",
  paid: "bg-purple-100 text-purple-800",
};

const Expenses = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [, setCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Load categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("expense_categories")
          .select("*")
          .eq("active", true);
        
        if (error) throw error;
        
        setCategories(data || []);
      } catch (error: any) {
        console.error("Error fetching categories:", error.message);
      }
    };
    
    fetchCategories();
  }, []);

  // Load expenses with filtering
  useEffect(() => {
    const fetchExpenses = async () => {
      setIsLoading(true);
      
      try {
        // Start building the query
        let query = supabase
          .from("expenses")
          .select(`
            *,
            expense_categories (id, name)
          `)
          .order("created_at", { ascending: false });
        
        // Apply status filter if not "all"
        if (statusFilter !== "all") {
          // Cast the statusFilter to the required type
          const validStatus = statusFilter as "draft" | "submitted" | "under_review" | "approved" | "rejected" | "paid";
          query = query.eq("status", validStatus);
        }
        
        // Apply search filter if provided
        if (searchQuery) {
          query = query.ilike("title", `%${searchQuery}%`);
        }
        
        // Execute the query
        const { data, error } = await query;
        
        if (error) throw error;
        
        // Format the expenses with category names
        const formattedExpenses = (data || []).map(expense => ({
          ...expense,
          category_name: expense.expense_categories?.name || "Uncategorized"
        }));
        
        setExpenses(formattedExpenses);
      } catch (error: any) {
        toast({
          title: "Error fetching expenses",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchExpenses();
  }, [toast, statusFilter, searchQuery]);

  // Handle expense deletion
  const handleDeleteExpense = async (id: string) => {
    if (!confirm("Are you sure you want to delete this expense?")) return;
    
    try {
      const { error } = await supabase
        .from("expenses")
        .delete()
        .eq("id", id);
      
      if (error) throw error;
      
      // Update local state to remove the deleted expense
      setExpenses(expenses.filter(expense => expense.id !== id));
      
      toast({
        title: "Expense deleted",
        description: "The expense has been deleted successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Handle expense submission
  const handleSubmitExpense = async (id: string) => {
    try {
      const { error } = await supabase
        .from("expenses")
        .update({ status: "submitted" })
        .eq("id", id);
      
      if (error) throw error;
      
      // Update local state to reflect the status change
      setExpenses(
        expenses.map(expense => 
          expense.id === id 
            ? { ...expense, status: "submitted" } 
            : expense
        )
      );
      
      toast({
        title: "Expense submitted",
        description: "Your expense has been submitted for approval",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Helper functions for formatting
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency 
    }).format(amount);
  };

  // Main rendering
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">My Expenses</h1>
        <Button onClick={() => navigate("/expenses/new")}>
          <Plus className="mr-2 h-4 w-4" /> New Expense
        </Button>
      </div>
      
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search expenses..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="w-full md:w-48">
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="submitted">Submitted</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {isLoading ? (
            <div className="text-center py-4">Loading expenses...</div>
          ) : expenses.length === 0 ? (
            <div className="text-center py-10">
              <FileText className="mx-auto h-12 w-12 text-gray-300" />
              <h3 className="mt-2 text-lg font-medium">No expenses found</h3>
              <p className="text-sm text-gray-500 mt-1">
                {searchQuery || statusFilter !== "all" 
                  ? "Try changing your search or filter" 
                  : "Create your first expense to get started"}
              </p>
              {!searchQuery && statusFilter === "all" && (
                <Button 
                  onClick={() => navigate("/expenses/new")} 
                  className="mt-4"
                >
                  <Plus className="mr-2 h-4 w-4" /> Create Expense
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Created</TableHead> {/* Changed from Category to Created */}
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {expenses.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className="font-medium">{expense.title}</TableCell>
                      <TableCell>{formatDate(expense.date)}</TableCell>
                      <TableCell>{formatCurrency(expense.amount, expense.currency)}</TableCell>
                      <TableCell>{formatDate(expense.created_at)}</TableCell> {/* Show created date instead of category */}
                      <TableCell>
                        <Badge 
                          className={statusColors[expense.status] || "bg-gray-100"}
                          variant="outline"
                        >
                          {expense.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          {(expense.status === "draft" || expense.status === "rejected") && (
                          <>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => handleSubmitExpense(expense.id)}
                            >
                              Submit
                            </Button>
                         
                              <Button 
                                size="sm" 
                                variant="ghost"
                                onClick={() => navigate(`/expenses/edit/${expense.id}`)}
                                title="Edit expense"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="sm" 
                                variant="ghost"
                                onClick={() => handleDeleteExpense(expense.id)}
                                title="Delete expense"
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                          </>
                          )}
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={() => navigate(`/expenses/${expense.id}`)}
                            title="View expense details"
                          >
                            View
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Expenses;
