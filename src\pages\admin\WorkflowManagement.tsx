
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

// Define types for workflow data
interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  is_default: boolean;
  created_at: string;
}

interface WorkflowStep {
  id: string;
  workflow_id: string;
  name: string;
  order_index: number;
  role: string;
  created_at: string;
}

export default function WorkflowManagement() {
  const [workflowTemplates, setWorkflowTemplates] = useState<WorkflowTemplate[]>([]);
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [newWorkflow, setNewWorkflow] = useState({ name: "", description: "" });
  const [newStep, setNewStep] = useState({ 
    name: "", 
    order: 1, 
    approver_role: "manager" 
  });
  const [, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("templates");

  useEffect(() => {
    fetchWorkflowTemplates();
  }, []);

  useEffect(() => {
    if (selectedWorkflow) {
      fetchWorkflowSteps(selectedWorkflow);
    }
  }, [selectedWorkflow]);

  const fetchWorkflowTemplates = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('workflow_templates')
        .select('*')
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      
      setWorkflowTemplates(data || []);
      if (data && data.length > 0 && !selectedWorkflow) {
        setSelectedWorkflow(data[0].id);
      }
    } catch (error) {
      console.error("Error fetching workflow templates:", error);
      toast.error("Failed to load workflow templates");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchWorkflowSteps = async (workflowId: string) => {
    try {
      const { data, error } = await supabase
        .from('workflow_steps')
        .select('*')
        .eq('workflow_id', workflowId)
        .order('order_index', { ascending: true });
        
      if (error) throw error;
      
      setWorkflowSteps(data || []);
    } catch (error) {
      console.error("Error fetching workflow steps:", error);
      toast.error("Failed to load workflow steps");
    }
  };

  const createWorkflowTemplate = async () => {
    try {
      if (!newWorkflow.name) {
        toast.error("Workflow name is required");
        return;
      }
      
      const { data, error } = await supabase
        .from('workflow_templates')
        .insert({
          name: newWorkflow.name,
          description: newWorkflow.description,
          is_default: false
        })
        .select()
        .single();
        
      if (error) throw error;
      
      toast.success("Workflow template created successfully");
      setNewWorkflow({ name: "", description: "" });
      fetchWorkflowTemplates();
      
      // Select the newly created workflow
      if (data) {
        setSelectedWorkflow(data.id);
        setActiveTab("steps");
      }
    } catch (error) {
      console.error("Error creating workflow template:", error);
      toast.error("Failed to create workflow template");
    }
  };

  const createWorkflowStep = async () => {
    try {
      if (!selectedWorkflow) {
        toast.error("Please select a workflow first");
        return;
      }
      
      if (!newStep.name) {
        toast.error("Step name is required");
        return;
      }
      
      const { error } = await supabase
        .from('workflow_steps')
        .insert({
          workflow_id: selectedWorkflow,
          name: newStep.name,
          order_index: newStep.order,
          role: newStep.approver_role // Map to role column
        });
        
      if (error) {
        console.error("Error creating workflow step:", error);
        throw error;
      }
      
      toast.success("Workflow step created successfully");
      setNewStep({ name: "", order: workflowSteps.length + 1, approver_role: "manager" });
      fetchWorkflowSteps(selectedWorkflow);
    } catch (error) {
      console.error("Error creating workflow step:", error);
      toast.error("Failed to create workflow step");
    }
  };

  const toggleWorkflowStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('workflow_templates')
        .update({ is_default: !currentStatus })
        .eq('id', id);
        
      if (error) throw error;
      
      toast.success(`Workflow ${currentStatus ? 'deactivated' : 'activated'} successfully`);
      fetchWorkflowTemplates();
    } catch (error) {
      console.error("Error updating workflow status:", error);
      toast.error("Failed to update workflow status");
    }
  };

  const deleteWorkflowStep = async (stepId: string) => {
    try {
      const { error } = await supabase
        .from('workflow_steps')
        .delete()
        .eq('id', stepId);
        
      if (error) throw error;
      
      toast.success("Workflow step deleted successfully");
      if (selectedWorkflow) {
        fetchWorkflowSteps(selectedWorkflow);
      }
    } catch (error) {
      console.error("Error deleting workflow step:", error);
      toast.error("Failed to delete workflow step");
    }
  };

  const viewWorkflowSteps = (workflowId: string) => {
    setSelectedWorkflow(workflowId);
    setActiveTab("steps");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Workflow Management</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="templates">Workflow Templates</TabsTrigger>
          <TabsTrigger value="steps">Workflow Steps</TabsTrigger>
        </TabsList>
        
        <TabsContent value="templates" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Workflow Templates</h2>
            <Dialog>
              <DialogTrigger asChild>
                <Button>Create New Workflow</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Workflow Template</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input 
                      id="name" 
                      value={newWorkflow.name} 
                      onChange={(e) => setNewWorkflow({...newWorkflow, name: e.target.value})}
                      placeholder="Expense Approval Workflow" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input 
                      id="description" 
                      value={newWorkflow.description} 
                      onChange={(e) => setNewWorkflow({...newWorkflow, description: e.target.value})}
                      placeholder="Standard workflow for expense approvals" 
                    />
                  </div>
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline">Cancel</Button>
                  </DialogClose>
                  <Button onClick={createWorkflowTemplate}>Create Workflow</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          
          <Card>
            <CardContent className="pt-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workflowTemplates.map((workflow) => (
                    <TableRow key={workflow.id}>
                      <TableCell className="font-medium">{workflow.name}</TableCell>
                      <TableCell>{workflow.description}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${workflow.is_default ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                          {workflow.is_default ? 'Active' : 'Inactive'}
                        </span>
                      </TableCell>
                      <TableCell>{new Date(workflow.created_at).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => viewWorkflowSteps(workflow.id)}
                          >
                            Configure Steps
                          </Button>
                          <Button 
                            variant={workflow.is_default ? "destructive" : "default"}
                            size="sm"
                            onClick={() => toggleWorkflowStatus(workflow.id, workflow.is_default)}
                          >
                            {workflow.is_default ? 'Deactivate' : 'Activate'}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                  {workflowTemplates.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4">
                        No workflow templates found. Create one to get started.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="steps" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">
              {selectedWorkflow && workflowTemplates.find(w => w.id === selectedWorkflow)?.name} - Workflow Steps
            </h2>
            <Select value={selectedWorkflow || ''} onValueChange={setSelectedWorkflow}>
              <SelectTrigger className="w-[280px]">
                <SelectValue placeholder="Select a workflow" />
              </SelectTrigger>
              <SelectContent>
                {workflowTemplates.map((workflow) => (
                  <SelectItem key={workflow.id} value={workflow.id}>
                    {workflow.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {selectedWorkflow && (
            <Card>
              <CardHeader>
                <CardTitle>Add New Step</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="step-name">Step Name</Label>
                    <Input 
                      id="step-name" 
                      value={newStep.name} 
                      onChange={(e) => setNewStep({...newStep, name: e.target.value})}
                      placeholder="Manager Approval" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="step-order">Order</Label>
                    <Input 
                      id="step-order" 
                      type="number" 
                      value={newStep.order} 
                      onChange={(e) => setNewStep({...newStep, order: parseInt(e.target.value)})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="step-role">Approver Role</Label>
                    <Select 
                      value={newStep.approver_role} 
                      onValueChange={(value) => setNewStep({...newStep, approver_role: value})}
                    >
                      <SelectTrigger id="step-role">
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="finance">Finance</SelectItem>
                        <SelectItem value="accounting">Accounting</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <Button className="mt-4" onClick={createWorkflowStep}>Add Step</Button>
              </CardContent>
            </Card>
          )}
          
          {selectedWorkflow && (
            <Card>
              <CardHeader>
                <CardTitle>Workflow Steps</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Approver Role</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {workflowSteps.map((step) => (
                      <TableRow key={step.id}>
                        <TableCell>{step.order_index}</TableCell>
                        <TableCell className="font-medium">{step.name}</TableCell>
                        <TableCell className="capitalize">{step.role}</TableCell>
                        <TableCell>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={() => deleteWorkflowStep(step.id)}
                          >
                            Delete
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                    {workflowSteps.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-4">
                          No steps found for this workflow. Add steps above.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
          
          {!selectedWorkflow && (
            <Card>
              <CardContent className="py-10 text-center">
                <p className="text-muted-foreground">Please select a workflow template to manage its steps.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
