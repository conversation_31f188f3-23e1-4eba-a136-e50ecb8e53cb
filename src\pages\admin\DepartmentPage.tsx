
import { useState, useEffect } from "react";
import {
  Card,
  CardContent
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON>alog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";
import { genericSupabase } from "@/integrations/supabase/client";
import { Department } from "@/types";

const DepartmentPage = () => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newDepartmentName, setNewDepartmentName] = useState("");
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null);

  useEffect(() => {
    fetchDepartments();
  }, []);

  const fetchDepartments = async () => {
    setIsLoading(true);
    try {
      // Updated table name with  prefix
      const { data, error } = await genericSupabase
        .from('departments')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;
      
      setDepartments(data || []);
    } catch (error) {
      console.error("Error fetching departments:", error);
      toast.error("Failed to load departments");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDialog = (department?: Department) => {
    if (department) {
      setEditingDepartment(department);
      setNewDepartmentName(department.name);
    } else {
      setEditingDepartment(null);
      setNewDepartmentName("");
    }
    setIsDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!newDepartmentName.trim()) {
      toast.error("Department name cannot be empty");
      return;
    }

    setIsSubmitting(true);
    try {
      if (editingDepartment) {
        // Update existing department - Updated table name with  prefix
        const { error } = await genericSupabase
          .from('departments')
          .update({ name: newDepartmentName })
          .eq('id', editingDepartment.id);

        if (error) throw error;
        
        toast.success("Department updated successfully");
      } else {
        // Create new department - Updated table name with  prefix
        const { error } = await genericSupabase
          .from('departments')
          .insert({ name: newDepartmentName });

        if (error) throw error;
        
        toast.success("Department created successfully");
      }
      
      setIsDialogOpen(false);
      fetchDepartments();
    } catch (error) {
      console.error("Error saving department:", error);
      toast.error("Failed to save department");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (departmentId: string) => {
    if (!confirm("Are you sure you want to delete this department? This action cannot be undone.")) {
      return;
    }

    try {
      // Updated table name with  prefix
      const { error } = await genericSupabase
        .from('departments')
        .delete()
        .eq('id', departmentId);

      if (error) throw error;
      
      toast.success("Department deleted successfully");
      fetchDepartments();
    } catch (error) {
      console.error("Error deleting department:", error);
      toast.error("Failed to delete department. It may be in use by tickets.");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Departments</h1>
          <p className="text-muted-foreground">Manage departments for ticket organization.</p>
        </div>
        <Button onClick={() => handleOpenDialog()}>Add Department</Button>
      </div>

      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <p>Loading departments...</p>
            </div>
          ) : departments.length === 0 ? (
            <div className="flex flex-col justify-center items-center h-40 text-center p-4">
              <p className="text-muted-foreground">No departments found</p>
              <Button 
                variant="link" 
                onClick={() => handleOpenDialog()}
                className="mt-2"
              >
                Add your first department
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Department Name</TableHead>
                  <TableHead className="w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {departments.map((department) => (
                  <TableRow key={department.id}>
                    <TableCell>{department.name}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleOpenDialog(department)}
                        >
                          Edit
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleDelete(department.id)}
                          className="text-destructive"
                        >
                          Delete
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Department Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingDepartment ? "Edit Department" : "Add Department"}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="department-name" className="text-sm font-medium">Department Name</label>
              <Input
                id="department-name"
                value={newDepartmentName}
                onChange={(e) => setNewDepartmentName(e.target.value)}
                placeholder="Enter department name"
              />
            </div>
          </div>

          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDialogOpen(false)} 
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DepartmentPage;
